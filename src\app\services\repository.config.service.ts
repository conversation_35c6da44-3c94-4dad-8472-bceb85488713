import { HttpClient } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { Observable, throwError } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { BehaviorSubject } from 'rxjs';
import { CreateEmailRemainderDto, EmailGroupCreateDto, UpdateEmailRemainderDto, EmailReminderListResponseDto, SkipReminderCycleDto } from "../components/repository-configuration/model/config-model";
import { EmailGroupDto, EmailMemberDto, EmailGroupUpdateDto, EmailGroupDetailDto } from "../components/repository-configuration/model/config-model";

@Injectable({
  providedIn: "root",
})
export class RepositoryConfigService {
  myAppUrl: string = "";
  private resetInProgressSubject = new BehaviorSubject<boolean>(false);
  displayConfigurationConflict: boolean = false;
  resetInProgress$ = this.resetInProgressSubject.asObservable();
  constructor(
    private readonly http: HttpClient,
    @Inject("BASE_URL") baseUrl: string
  ) {
    this.myAppUrl = baseUrl;
  }

  errorHandler(error: any) {
    return throwError(() => error);
  }

  getRepositoryConfigs(id: number): Observable<any> {
    return this.http.get<any>(this.myAppUrl + "api/get-document-config/" + id).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }
  getRepositoryConfigsForMultipleCompanies(featureId: number, companyIds: number[]): Observable<any> {
    return this.http.post<any>(this.myAppUrl + `api/get-combined-document-config/${featureId}`, companyIds).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  updateRepositoryConfig(config: any): Observable<any> {
    return this.http.post<any>(this.myAppUrl + `api/update-document-config`, config).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  getRepositoryStructureData(encryptedCompanyId: string, featureId: number): Observable<any> {
    return this.http.get<any>(`${this.myAppUrl}api/get-repository-structure/${encryptedCompanyId}/${featureId}`).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  getPortfolioCompanies(): Observable<any> {
    return this.http.post<any>(this.myAppUrl + `api/get-portfolio-companies`, null).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  getFunds(): Observable<any> {
    return this.http.post<any>(this.myAppUrl + `api/get-fund-list`, null).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  uploadDocumentstoServer(entriptedid: string, formData: FormData): Observable<any> {
    return this.http.post<any>(this.myAppUrl + `api/upload-documents/${entriptedid}`, formData).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  getDocumentList(entriptedid: string, docReqest: any): Observable<any> {
    return this.http.post<any>(this.myAppUrl + `api/get-documents/${entriptedid}`, docReqest).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  /**
   * Deletes specified documents from the repository
   * @param encryptedCompanyId Encrypted portfolio company ID
   * @param deleteRequest Object containing the folder path and document IDs to delete
   * @returns API response with the result of the delete operation
   */
  deleteDocuments(encryptedCompanyId: string, deleteRequest: any): Observable<any> {
    return this.http.post<any>(this.myAppUrl + `api/delete-documents/${encryptedCompanyId}`, deleteRequest).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  setResetInProgress(state: boolean): void {
    this.resetInProgressSubject.next(state);
  }

  getCategoriesAndDocumentTypes(companyId: number): Observable<any> {
    return this.http.get<any>(this.myAppUrl + `api/user-information/categories-and-doctypes/${companyId}`)
      .pipe(
        map((response) => response),
        catchError(this.errorHandler)
      );
  }

  /**
   * Creates a new user information record with associated document types
   * @param userInfoData Data object containing the user information details
   * @returns API response with the result of the create operation
   */
  createUserInformation(userInfoData: any): Observable<any> {
    return this.http.post<any>(this.myAppUrl + 'api/user-information/create-user', userInfoData).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  /**
   * Gets a list of user information records with their associated document types
   * @param companyId The company ID to filter users by
   * @returns List of user information records with document types
   */
  getUserInformationByCompany(companyId: number): Observable<any> {
    return this.http.get<any>(this.myAppUrl + `api/user-information/user-info-by-company/${companyId}`).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  createEmailGroup(emailGroup: EmailGroupCreateDto): Observable<any> {
    return this.http.post<any>(this.myAppUrl + 'api/email-groups/create', emailGroup).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  checkDuplicateName(emailGroup: any): Observable<any> {
    return this.http.post<any>(this.myAppUrl + 'api/email-groups/check-duplicate-name', emailGroup).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  getEmailGroups(): Observable<EmailGroupDto[]> {
    return this.http.get<EmailGroupDto[]>(this.myAppUrl + 'api/email-groups/all').pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }
  getEmailListByGroupId(groupId: number): Observable<EmailMemberDto[]> {
    return this.http.get<EmailMemberDto[]>(this.myAppUrl + `api/email-groups/${groupId}/members`).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  getEmailGroupDetails(groupId: number): Observable<EmailGroupDetailDto> {
    return this.http.get<EmailGroupDetailDto>(this.myAppUrl + `api/email-groups/${groupId}/detail`).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  updateEmailGroup(groupId: number, emailGroup: EmailGroupUpdateDto): Observable<any> {
    return this.http.put<any>(this.myAppUrl + `api/email-groups/${groupId}`, emailGroup).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  createEmailRemainder(remainder: CreateEmailRemainderDto): Observable<any> {
    return this.http.post<any>(this.myAppUrl + 'api/email-reminders/create', remainder).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }
  getEmailReminderDefaults(id: string): Observable<any> {
    return this.http.get<any>(this.myAppUrl + 'api/email-reminders/' + id).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

      getEmailReminderEdit(id: string): Observable<any> {
        return this.http.get<any>(this.myAppUrl + 'api/email-reminders/edit/'+ id).pipe(
            map((response) => response),
            catchError(this.errorHandler)
        );
      }

      updateEmailReminder(remainder: UpdateEmailRemainderDto): Observable<any> {
        return this.http.post<any>(this.myAppUrl + 'api/update-email-reminders', remainder).pipe(
            map((response) => response),
            catchError(this.errorHandler)
        );
      }

  getEmailReminders(): Observable<EmailReminderListResponseDto[]> {
    return this.http.get<EmailReminderListResponseDto[]>(this.myAppUrl + 'api/email-reminders').pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  getEmailReminderDetails(reminderId: string): Observable<any> {
    return this.http.get<any>(this.myAppUrl + 'api/email-reminders/' + reminderId + '/details').pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  /**
   * Gets user information by ID with associated document types
   * @param userInformationId The ID of the user information record to retrieve
   * @returns User information with document types
   */
  getUserInformationById(userInformationId: number): Observable<any> {
    return this.http.get<any>(this.myAppUrl + `api/user-information/user-info/${userInformationId}`).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  /**
   * Updates an existing user information record with associated document types
   * @param userInfoData Data object containing the updated user information details
   * @returns API response with the result of the update operation
   */
  updateUserInformation(userInfoData: any): Observable<any> {
    return this.http.put<any>(this.myAppUrl + 'api/user-information/update-user', userInfoData).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  deleteEmailReminder(reminderId: string): Observable<any> {
    return this.http.delete<any>(`${this.myAppUrl}api/email-reminders/${reminderId}`).pipe(
      catchError(this.errorHandler)
    );
  }

  skipReminderCycle(skipData: SkipReminderCycleDto): Observable<any> {
    return this.http.post<any>(`${this.myAppUrl}api/email-reminders/skip-cycle`, skipData).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  /**
   * Deletes an email group by its ID
   * @param groupId The ID of the email group to delete
   * @returns API response with the result of the delete operation
   */
  deleteEmailGroup(groupId: number): Observable<any> {
    return this.http.delete<any>(`${this.myAppUrl}api/email-groups/${groupId}`).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  /**
   * Deletes selected email members from a group
   * @param groupId The ID of the email group
   * @param deleteRequest Object containing the member IDs to delete
   * @returns API response with the result of the delete operation
   */
  deleteEmailMembers(groupId: number, deleteRequest: { memberIds: number[] }): Observable<any> {
    return this.http.delete<any>(`${this.myAppUrl}api/email-groups/${groupId}/members`, {
      body: deleteRequest
    }).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }

  downloadDocument(encryptedCompanyId: string, documentId: string, folderPath?: string): Observable<Blob> {
    let url = `${this.myAppUrl}api/download-document/${encryptedCompanyId}/${documentId}`;
    if (folderPath) {
      url += `?folderPath=${encodeURIComponent(folderPath)}`;
    }
    return this.http.get(url, { responseType: 'blob' }).pipe(
      catchError(this.errorHandler)
    );
  }

  /**
   * Deletes a user information record by its ID
   * @param userInformationId The ID of the user information record to delete
   * @returns API response with the result of the delete operation
   */
  deleteUserInformation(userInformationId: number): Observable<any> {
    return this.http.delete<any>(this.myAppUrl + `api/user-information/delete-user/${userInformationId}`).pipe(
      map((response) => response),
      catchError(this.errorHandler)
    );
  }
}