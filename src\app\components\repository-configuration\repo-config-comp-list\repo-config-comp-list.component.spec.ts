import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RepositoryConfigurationCompanyListComponent } from './repo-config-comp-list.component';
import { FormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { By } from '@angular/platform-browser';
import { RepositoryConfigService } from 'src/app/services/repository.config.service';
import { PageConfigurationService } from 'src/app/services/page-configuration.service';

class MockPageConfigurationService {
  getPageConfigSettingById(id: number) {
    return of({
      fieldValueList: [
        { name: 'Funds', displayName: 'Funds' },
        { name: 'Portfolio Company', displayName: 'Portfolio Company' }
      ]
    });
  }
}
import { of, throwError } from 'rxjs';

class MockRepositoryConfigService {
  getPortfolioCompanies() {
    return of([
      { companyName: 'Company A', portfolioCompanyID: 1 },
      { companyName: 'Company B', portfolioCompanyID: 2 }
    ]);
  }

  getFunds() {
    return of([
      { fundName: 'Fund A', fundID: '1' },
      { fundName: 'Fund B', fundID: '2' }
    ]);
  }
  resetInProgress$ = of(false);
}

describe('RepositoryConfigurationCompanyListComponent', () => {  

  it('should fetch portfolio companies and update companies and showFunds', () => {
    const mockCompanies = [
      { companyName: 'Company X', portfolioCompanyID: 10 },
      { companyName: 'Company Y', portfolioCompanyID: 20 }
    ];
    (mockRepositoryConfigService as any).getPortfolioCompanies = () => of(mockCompanies);
    spyOn(mockRepositoryConfigService, 'getPortfolioCompanies').and.callThrough();
    const selectedFundsSpy = spyOn(component.selectedFunds, 'emit');
    const isFundChangeSpy = spyOn(component.isFundChange, 'emit');
    component.getFundsAndPcs();
    expect(mockRepositoryConfigService.getPortfolioCompanies).toHaveBeenCalled();
    expect(component.showFunds).toBeFalse();
    expect(component.companies.length).toBe(2);
    expect(component.companies[0].name).toBe('Company X');
    expect(component.isLoader).toBeFalse();
    expect(selectedFundsSpy).toHaveBeenCalledWith(null);
    expect(isFundChangeSpy).toHaveBeenCalledWith(false);
  });

  it('should fetch funds and update funds and showFunds', () => {
    const mockFunds = [
      { fundID: '11', fundName: 'Fund X' },
      { fundID: '22', fundName: 'Fund Y' }
    ];
    (mockRepositoryConfigService as any).getFunds = () => of(mockFunds);
    spyOn(mockRepositoryConfigService, 'getFunds').and.callThrough();
    const selectedCompaniesSpy = spyOn(component.selectedCompanies, 'emit');
    const isFundChangeSpy = spyOn(component.isFundChange, 'emit');
    component.getFundListData();
    expect(mockRepositoryConfigService.getFunds).toHaveBeenCalled();
    expect(component.showFunds).toBeTrue();
    expect(component.funds.length).toBe(2);
    expect(component.funds[0].fundName).toBe('Fund X');
    expect(component.isLoader).toBeFalse();
    expect(selectedCompaniesSpy).toHaveBeenCalledWith(null);
    expect(isFundChangeSpy).toHaveBeenCalledWith(true);
  });

  it('should emit selected funds in fundCheckSelection when funds are selected', () => {
    const emitSpy = spyOn(component.selectedFunds, 'emit');
    component.funds = [
      { fundName: 'Fund 1', fundId: '1', selected: true },
      { fundName: 'Fund 2', fundId: '2', selected: false }
    ];
    component.fundCheckSelection();
    expect(emitSpy).toHaveBeenCalledWith([
      jasmine.objectContaining({ fundName: 'Fund 1', fundId: '1', selected: true })
    ]);
  });

  it('should emit empty array in fundCheckSelection when no funds are selected', () => {
    const emitSpy = spyOn(component.selectedFunds, 'emit');
    component.funds = [
      { fundName: 'Fund 1', fundId: '1', selected: false },
      { fundName: 'Fund 2', fundId: '2', selected: false }
    ];
    component.fundCheckSelection();
    expect(emitSpy).toHaveBeenCalledWith([]);
  });
  let component: RepositoryConfigurationCompanyListComponent;
  let fixture: ComponentFixture<RepositoryConfigurationCompanyListComponent>;
  let mockRepositoryConfigService: RepositoryConfigService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [RepositoryConfigurationCompanyListComponent],
      imports: [FormsModule, HttpClientTestingModule],
      providers: [
        { provide: RepositoryConfigService, useClass: MockRepositoryConfigService },
        { provide: PageConfigurationService, useClass: MockPageConfigurationService },
        { provide: 'BASE_URL', useValue: '/' }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(RepositoryConfigurationCompanyListComponent);
    component = fixture.componentInstance;
    mockRepositoryConfigService = TestBed.inject(RepositoryConfigService);
  });

  it('should handle resetInProgress$ subscription', () => {
    (mockRepositoryConfigService as any).resetInProgress$ = of(true);

    component.ngOnInit();
    expect(component.resetInProgress).toBe(true);
  });

  it('should handle error when getting portfolio companies', () => {
    spyOn(mockRepositoryConfigService, 'getPortfolioCompanies').and.returnValue(
      throwError(() => new Error('Test error'))
    );

    component.getFundsAndPcs();
    expect(component.isLoader).toBe(false);
  });

  it('should handle error when getting funds', () => {
    spyOn(mockRepositoryConfigService, 'getFunds').and.returnValue(
      throwError(() => new Error('Test error'))
    );

    component.getFundListData();
    expect(component.isLoader).toBe(false);
  });

  it('should fetch fund list and update funds and showFunds', () => {
    // Arrange
    const mockFunds = [
      { fundID: '1', fundName: 'Fund 1' },
      { fundID: '2', fundName: 'Fund 2' }
    ];
    // Add getFunds to the mock service
    (mockRepositoryConfigService as any).getFunds = () => of(mockFunds);
    spyOn(mockRepositoryConfigService, 'getFunds').and.callThrough();
    // Act
    component.getFundListData();
    // Assert
    expect(mockRepositoryConfigService.getFunds).toHaveBeenCalled();
    expect(component.showFunds).toBeTrue();
    expect(component.funds.length).toBe(2);
    expect(component.funds[0].fundName).toBe('Fund 1');
    expect(component.isLoader).toBeFalse();
  });

  it('should handle error when getting fund list', () => {
    // Arrange
    spyOn(mockRepositoryConfigService, 'getFunds').and.returnValue(throwError(() => new Error('Test error')));
    // Act
    component.getFundListData();
    // Assert
    expect(component.isLoader).toBeFalse();
  });

  it('should update searchedCompanies on input change', () => {
    component.companies = [
      { name: 'Test Company 1', selected: false, companyId: '1' },
      { name: 'Test Company 2', selected: false, companyId: '2' },
      { name: 'Other Company', selected: false, companyId: '3' }
    ];

    component.onInputChange({ target: { value: 'Test' } } as unknown as Event);

    expect(component.searchedCompanies.length).toBe(2);
    expect(component.searchedCompanies.every(c => c.name.includes('Test'))).toBe(true);
  });

  it('should handle select all toggling correctly', () => {
    component.companies = [
      { name: 'Company 1', selected: false, companyId: '1' },
      { name: 'Company 2', selected: false, companyId: '2' }
    ];
    component.selectAll = true;
    component.toggleSelectAll();

    expect(component.companies.every(c => c.selected)).toBe(true);
    component.selectAll = false;
    component.toggleSelectAll();
    expect(component.companies.every(c => !c.selected)).toBe(true);
  });

it('should allow user to switch between Portfolio Company and Funds, search, and select companies', async () => {
  // Initial load: should show companies
  component.ngOnInit();
  fixture.detectChanges();
  await fixture.whenStable();

  // Simulate user clicking on "Funds" tab
  const tabElements = fixture.debugElement.queryAll(By.css('.col-6.cursor-pointer'));
  expect(tabElements.length).toBeGreaterThan(1);
  const fundsTab = tabElements[1];
  if (!fundsTab) {
    fail('Funds tab element not found. Check if the template renders the expected tab elements.');
    return;
  }
  fundsTab.triggerEventHandler('click', null);
  fixture.detectChanges();
  await fixture.whenStable();

  expect(component.showFunds).toBeTrue();
  expect(component.funds.length).toBeGreaterThan(0);

  // Simulate user clicking back to "Portfolio Company" tab
  const pcTab = tabElements[0];
  if (!pcTab) {
    fail('Portfolio Company tab element not found. Check if the template renders the expected tab elements.');
    return;
  }
  pcTab.triggerEventHandler('click', null);
  fixture.detectChanges();
  await fixture.whenStable();

  expect(component.showFunds).toBeFalse();
  expect(component.companies.length).toBeGreaterThan(0);

  // Simulate user typing in search box
  component.searchTerm = 'Company A';
  component.onInputChange({ target: { value: 'Company A' } } as any);
  fixture.detectChanges();
  await fixture.whenStable();

  expect(component.searchedCompanies.length).toBe(1);
  expect(component.searchedCompanies[0].name).toBe('Company A');

  // Simulate selecting the company
  component.searchedCompanies[0].selected = true;
  component.checkSelection();
  fixture.detectChanges();

  expect(component.companies.find(c => c.name === 'Company A')?.selected).toBeTrue();
});

it('should return only selected funds in selectedFundItems', () => {
    component.funds = [
      { fundName: 'Fund 1', fundId: '1', selected: true },
      { fundName: 'Fund 2', fundId: '2', selected: false },
      { fundName: 'Fund 3', fundId: '3', selected: true }
    ];
    const selected = component.selectedFundItems;
    expect(selected.length).toBe(2);
    expect(selected.every(f => f.selected)).toBeTrue();
    expect(selected.map(f => f.fundName)).toEqual(['Fund 1', 'Fund 3']);
  });

  it('should return only non-selected funds in nonSelectedFundItems', () => {
    component.funds = [
      { fundName: 'Fund 1', fundId: '1', selected: true },
      { fundName: 'Fund 2', fundId: '2', selected: false },
      { fundName: 'Fund 3', fundId: '3', selected: false }
    ];
    const nonSelected = component.nonSelectedFundItems;
    expect(nonSelected.length).toBe(2);
    expect(nonSelected.every(f => !f.selected)).toBeTrue();
    expect(nonSelected.map(f => f.fundName)).toEqual(['Fund 2', 'Fund 3']);
  });
});

describe('RepositoryConfigurationCompanyListComponent - Additional Coverage', () => {
  let component: RepositoryConfigurationCompanyListComponent;
  let fixture: ComponentFixture<RepositoryConfigurationCompanyListComponent>;
  let mockRepositoryConfigService: RepositoryConfigService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [RepositoryConfigurationCompanyListComponent],
      imports: [FormsModule, HttpClientTestingModule],
      providers: [
        { provide: RepositoryConfigService, useClass: MockRepositoryConfigService },
        { provide: PageConfigurationService, useClass: MockPageConfigurationService },
        { provide: 'BASE_URL', useValue: '/' }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(RepositoryConfigurationCompanyListComponent);
    component = fixture.componentInstance;
    mockRepositoryConfigService = TestBed.inject(RepositoryConfigService);
  });

  it('should apply preSelectedCompanies on load', () => {
    component.preSelectedCompanies = [{ companyId: 1 }];
    component.companies = [
      { name: 'Company A', companyId: '1', selected: false },
      { name: 'Company B', companyId: '2', selected: false }
    ];
    const emitSpy = spyOn(component.selectedCompanies, 'emit');
    (component as any).applyPreselections();
    expect(component.companies[0].selected).toBeTrue();
    expect(emitSpy).toHaveBeenCalledWith([jasmine.objectContaining({ companyId: '1' })]);
  });

  it('should debounce function calls', (done) => {
    let callCount = 0;
    const debounced = component.debounce(() => { callCount++; }, 100);
    debounced();
    debounced();
    setTimeout(() => {
      expect(callCount).toBe(1);
      done();
    }, 150);
  });

  it('should handle select all with filtered companies', () => {
    component.companies = [
      { name: 'Alpha', selected: false, companyId: '1' },
      { name: 'Beta', selected: false, companyId: '2' }
    ];
    component.searchTerm = 'Alpha';
    component.selectAll = true;
    component.toggleSelectAll();
    expect(component.companies[0].selected).toBeTrue();
    expect(component.companies[1].selected).toBeFalse();
  });

  it('should set loader true while fetching companies/funds', () => {
    spyOn(component['repositoryConfigService'], 'getPortfolioCompanies').and.returnValue(of([]));
    component.getFundsAndPcs();
    expect(component.isLoader).toBeFalse(); // loader is set to false after fetch

    spyOn(component['repositoryConfigService'], 'getFunds').and.returnValue(of([]));
    component.getFundListData();
    expect(component.isLoader).toBeFalse();
  });

  it('should handle empty companies/funds from service', () => {
    spyOn(component['repositoryConfigService'], 'getPortfolioCompanies').and.returnValue(of([]));
    component.getFundsAndPcs();
    expect(component.companies.length).toBe(0);

    spyOn(component['repositoryConfigService'], 'getFunds').and.returnValue(of([]));
    component.getFundListData();
    expect(component.funds.length).toBe(0);
  });

    it('filteredCompanies should return companies matching searchTerm', () => {
      component.companies = [
        { name: 'Alpha', selected: false, companyId: '1' },
        { name: 'Beta', selected: false, companyId: '2' }
      ];
      component.searchTerm = 'alpha';
      expect(component.filteredCompanies.length).toBe(1);
      expect(component.filteredCompanies[0].name).toBe('Alpha');
    });

    it('toggleSelectAll should set displayConfigurationConflict to false when deselecting all', () => {
      component.companies = [
        { name: 'Alpha', selected: true, companyId: '1' }
      ];
      component.selectAll = false;
      (component as any).repositoryConfigService.displayConfigurationConflict = true;
      component.toggleSelectAll();
      expect((component as any).repositoryConfigService.displayConfigurationConflict).toBe(false);
    });

    it('checkSelection should set selectAll to false if not all companies are selected', () => {
      component.companies = [
        { name: 'Alpha', selected: true, companyId: '1' },
        { name: 'Beta', selected: false, companyId: '2' }
      ];
      component.checkSelection();
      expect(component.selectAll).toBe(false);
    });

    it('onInputChange should clear searchedCompanies if no match', () => {
      component.companies = [
        { name: 'Alpha', selected: false, companyId: '1' }
      ];
      component.onInputChange({ target: { value: 'Zeta' } } as any);
      expect(component.searchedCompanies.length).toBe(0);
    });

    it('applyPreselections should do nothing if preSelectedCompanies is empty', () => {
      component.preSelectedCompanies = [];
      component.companies = [
        { name: 'Alpha', selected: false, companyId: '1' }
      ];
      const emitSpy = spyOn(component.selectedCompanies, 'emit');
      (component as any).applyPreselections();
      expect(component.companies[0].selected).toBe(false);
      expect(emitSpy).not.toHaveBeenCalled();
    });

    it('should handle getPageConfigSetting with no fieldValueList', () => {
      const pageConfigService = TestBed.inject(PageConfigurationService);
      spyOn(pageConfigService, 'getPageConfigSettingById').and.returnValue(of({}));
      component.getPageConfigSetting();
      expect(component.fieldValueList).toEqual([]);
    });

    it('should handle getPageConfigSetting with only Funds', () => {
      const pageConfigService = TestBed.inject(PageConfigurationService);
      spyOn(pageConfigService, 'getPageConfigSettingById').and.returnValue(of({
        fieldValueList: [{ name: 'Funds', displayName: 'Funds' }]
      }));
      const getFundListDataSpy = spyOn(component, 'getFundListData');
      component.getPageConfigSetting();
      expect(component.showFundSection).toBeTrue();
      expect(component.showPCSection).toBeFalse();
      expect(getFundListDataSpy).toHaveBeenCalled();
    });

    it('should handle getPageConfigSetting with only Portfolio Company', () => {
      const pageConfigService = TestBed.inject(PageConfigurationService);
      spyOn(pageConfigService, 'getPageConfigSettingById').and.returnValue(of({
        fieldValueList: [{ name: 'Portfolio Company', displayName: 'Portfolio Company' }]
      }));
      const getFundsAndPcsSpy = spyOn(component, 'getFundsAndPcs');
      component.getPageConfigSetting();
      expect(component.showFundSection).toBeFalse();
      expect(component.showPCSection).toBeTrue();
      expect(getFundsAndPcsSpy).toHaveBeenCalled();
    });
  });