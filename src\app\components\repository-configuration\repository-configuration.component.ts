import { Component, OnInit } from '@angular/core';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { Router, ActivatedRoute } from '@angular/router';
import { PageConfigurationService } from 'src/app/services/page-configuration.service';

@Component({
  selector: 'app-repository-configuration',
  templateUrl: './repository-configuration.component.html',
  styleUrls: ['./repository-configuration.component.scss']
})
export class RepositoryConfigurationComponent implements OnInit {
  selectedCompanies: any[] = [];
  selectedFunds: any[] = [];
  activeTab: string = 'Data Collection';
  isFund: boolean = false;
  params: {};
  emailNotificationDisplayName: any;
  dataCollectionDisplayName: any;
  canViewEmailNotification: any;
  canViewDataCollection: any;

  tabs: ITab[] = [];

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private pageConfigurationService: PageConfigurationService,
  ) { 
    this.params = this.route.snapshot.queryParams;
  }

  ngOnInit(): void {
    // Handle query parameters for tab selection
    this.route.queryParams.subscribe(params => {
      if (params['activeTab']) {
        this.activeTab = params['activeTab'];
        // Find and update the active state of the tabs
        this.tabs.forEach(t => {
          t.active = (t.aliasname === this.activeTab);
        });
      }
    });

    this.getPageConfigSetting();
  }

  getPageConfigSetting(): void {
    this.pageConfigurationService.getPageConfigSettingById(12).subscribe(
      (result: any) => {
        if (result && result.subPageList) {          
          this.tabs = result.subPageList
            .filter((x: any) => x.isActive)
            .map((x: any) => ({
              name: x.displayName, 
              aliasname: x.name, 
              hidden: !x.isActive,              
            }));
          // Set activeTab to first tab's name
          if (this.tabs.length > 0) {
            this.activeTab = this.tabs[0].aliasname;
            this.tabs[0].active = true;             
          }
        }                
      }
    );
  }

  onCompanySelected(companies: any[]): void {
    this.selectedCompanies = companies;
  }

  onFundSelected(funds: any[]): void {
    this.selectedFunds = funds; 
  }

  onIsFundChange(isFund: boolean): void {
    this.isFund = isFund;
  }

  onTabClick(tab: ITab): void {    
    this.activeTab = tab.aliasname;
    this.tabs.forEach(t => {
      t.active = (t.aliasname === tab.aliasname);
    });
    if ((this.params && this.params['activeTab']) && this.activeTab === 'Data Collection') {
      window.history.replaceState({}, '', '#/repository-configuration');
    }
  }

  navigateToEmailConfig(): void {    
      this.router.navigate(['/email-configuration']);    
  }
}