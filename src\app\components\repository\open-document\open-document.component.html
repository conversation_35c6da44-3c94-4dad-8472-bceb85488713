<div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 repository-open-document custom-calendar-width" *ngIf="isEnableView">
    <div class="row mr-0 ml-0 ">
        <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
            <div class="row mr-0 ml-0">
                <div class="add-user-component left-pannel-style">
                    <div class="card repository-page-height">
                        <div class="card-header">
                            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                                <div class="row mr-0 ml-0 d-block">
                                    <div class="float-left custom-margin-top">
                                        <span class="document-details">Document Details</span>
                                    </div>
                                    <div class="float-right">
                                        <kendo-combobox  id="doc-status" [clearButton]="false" [(ngModel)]="selectDocumentStatus" #currency="ngModel" [fillMode]="'flat'"
                                            [filterable]="false" name="currency"
                                            class="k-select-medium k-select-flat-custom k-dropdown-height-28  k-dropdown-width-192 no-bg" [size]="'medium'"
                                            [data]="AllStatusTypes" [filterable]="true" [value]="selectDocumentStatus" [valuePrimitive]="false" textField="name"
                                            valueField="id" (valueChange)="OnStatusChange(selectDocumentStatus)">
                                            <ng-template kendoComboBoxSelectedItemTemplate let-dataItem>
                                                <span class="{{getClass(selectDocumentStatus)}}"></span>
                                                <span class="drop_down_item_label" id="{{selectDocumentStatus.name}}">{{selectDocumentStatus.name}}</span>
                                            </ng-template>
                                            <ng-template kendoComboBoxItemTemplate let-item>
                                                    <span class="{{item.name.replace(' ','_')}} d-inline-block"></span>
                                                    <span class="drop_down_item_label d-inline-block" id="{{item.name}}">{{item.name}}</span>  
                                            </ng-template>
                                        </kendo-combobox>
                                    </div>

                                </div>
                            </div>

                        </div>

                        <div class="card-body padding-style-control edit-repository">
                            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                                <div class="row mr-0 ml-0">
                                    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                                        <div class="row mr-0 ml-0">
                                            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 mb-3">
                                                <label class="Caption-M">
                                                    Document Name
                                                </label>
                                                <div>
                                                    <div class="pl-0" *ngIf="!editMode; else DocName">
                                                        {{selectedDocument?.name}}
                                                    </div>
                                                    <ng-template #DocName>
                                                        <input (input)="hasDocUpdated($event,'name')" type="text"
                                                            pInputText required [(ngModel)]="selectedDocument.name"
                                                            (blur)="vaidateDocExists()" class="opn-doc-wid pl-0"
                                                            [ngClass]="hasDocNameUpdated ? ('openDocInputText fontStyle14 activateInputField') : 'openDocInputText fontStyle14'" />
                                                        <div *ngIf="docNameValidation != ''"
                                                            class="nep-error positionabsolute od_ValidationFull">
                                                            {{docNameValidation}}
                                                        </div>
                                                        <div *ngIf="documentError.ShowDocumentNameError"
                                                            class="nep-error">Please enter document name</div>
                                                    </ng-template>
                                                </div>
                                            </div>
                                            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                                                <div class="row mr-0 ml-0">
                                                    <div class="col-6 pl-0 pr-12">
                                                        <div class="document-date mb-3">
                                                            <label class="Caption-M">
                                                                Document Date
                                                            </label>
                                                            <div
                                                                [ngClass]="!editMode ? 'textColor  fontStyle14 w100' : 'textColor  fontStyle14 w100'">
                                                                <span *ngIf="!editMode; else enable">
                                                                    <span class="mL"
                                                                        *ngIf="selectedDocument!== null && selectedDocument.dateOfDocument !== null;">
                                                                        {{selectedDocument?.dateOfDocument |
                                                                        date:'dd-MMM-yyyy'}}
                                                                    </span>
                                                                    <span class="mL"
                                                                        *ngIf="selectedDocument!== null && selectedDocument.dateOfDocument === null;">
                                                                        -
                                                                    </span>
                                                                </span>
                                                                <ng-template #enable>
                                                                        <kendo-datepicker calendarType="classic" class="k-picker-custom-flat k-datepicker-height-35" [format]="format"
                                                                            [fillMode]="'flat'" placeholder="Select Month" id="docDate" name="docDate"
                                                                            [(ngModel)]="selectedDocument.dateOfDocument" [value]="getFormattedDate(selectedDocument.dateOfDocument)"
                                                                            (valueChange)="hasDocUpdated(selectedDocument.dateOfDocument,'dateOfDocument')"></kendo-datepicker>
                                                                </ng-template>
                                                                <div *ngIf="editMode && documentError.showDocumentDateError"
                                                                    class="nep-error positionabsolute od_Validation">
                                                                    Please select date
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="document-type mb-3">
                                                            <label class="Caption-M">
                                                                Type
                                                            </label>
                                                            <div [ngClass]="!editMode ? 'textColor ' : 'textColor '">
                                                                <div class="fontStyle14 mL"
                                                                    *ngIf="!editMode; else DocType">
                                                                    {{selectedDocument?.documentTypeName}}
                                                                </div>
                                                                <ng-template #DocType>
                                                                    <kendo-combobox  [valuePrimitive]="true" [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="selectedDocumentType"
                                                                        #DocumentType="ngModel" [fillMode]="'flat'" [filterable]="true" name="DocumentType" [virtual]="virtual"
                                                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                                                        [data]="documentTypes" [filterable]="true" textField="name" valueField="id"
                                                                        [placeholder]="selectedDocument.documentTypeName !== '-' ? selectedDocument.documentTypeName : 'Select type'"
                                                                        (valueChange)="onDocumentTypeChanged()">
                                                                    </kendo-combobox>
                                                                    <div *ngIf="documentError.ShowDocTypeError"
                                                                        class="nep-error positionabsolute od_Validation">
                                                                        Please choose type</div>
                                                                </ng-template>
                                                            </div>
                                                        </div>
                                                        <div class="document-sub-type mb-3">
                                                            <label class="Caption-M">
                                                                Sub Type
                                                            </label>
                                                            <div [ngClass]="!editMode ? 'textColor ' : 'textColor '">
                                                                <div class="fontStyle14 mL"
                                                                    *ngIf="!editMode; else DocSubType">
                                                                    {{selectedDocument?.documentSubTypeName}}
                                                                </div>
                                                                <ng-template #DocSubType>
                                                                    <kendo-combobox  [valuePrimitive]="true" [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="selectedSubDocumentType"
                                                                        #DocumentSubType="ngModel" [fillMode]="'flat'" [filterable]="true" name="DocumentSubType" [virtual]="virtual"
                                                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                                                        [data]="documentSubTypes" [filterable]="true" textField="name" valueField="id"
                                                                        [placeholder]="selectedDocument.documentSubTypeName !== '-' ? selectedDocument.documentSubTypeName : 'Select sub type'"
                                                                        (valueChange)="onDocumentSubTypeChanged()">
                                                                    </kendo-combobox>
                                                                    <div *ngIf="documentError.ShowSubTypeError"
                                                                        class="nep-error positionabsolute od_Validation">
                                                                        Please choose sub-type
                                                                    </div>
                                                                </ng-template>
                                                            </div>
                                                        </div>
                                                        <div class="current-folder mb-3">
                                                            <label class="Caption-M">
                                                                Current Folder
                                                            </label>
                                                            <div class="fontStyle14">
                                                                <div class="ml-20" [ngClass]="editMode ? 'folderType' : ''"
                                                                    *ngIf="!editMode; else AvailableFolders">
                                                                    <img class="folderIcon"
                                                                        src="assets/dist/images/Folder.svg" alt="" />
                                                                    <span
                                                                        class="textColor fontStyle14">{{selectedDocument?.folderName}}</span>
                                                                </div>
                                                                <ng-template #AvailableFolders>
                                                                    <div>
                                                                        <kendo-combobox  [clearButton]="false" [kendoDropDownFilter]="filterSettings" [(ngModel)]="selectedDocument.folderName"
                                                                        #DocumentSubType="ngModel" [fillMode]="'flat'" [filterable]="true" name="DocumentSubType" [virtual]="virtual"
                                                                        class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35" [size]="'medium'"
                                                                        [data]="folders"  textField="name" valueField="name" [valuePrimitive] = "true"
                                                                        placeholder="Search or select folders"
                                                                        (valueChange)="OnFoldersSelected($event)">
                                                                        <ng-template kendoComboBoxItemTemplate let-name>
                                                                            <img class="folderIcon"
                                                                                    src="assets/dist/images/Folder.svg"
                                                                                    alt="" />
                                                                                <span>{{name}}</span>
                                                                        </ng-template>
                                                                    </kendo-combobox>
                                                                    </div>
                                                                </ng-template>
                                                            </div>
                                                        </div>

                                                    </div>
                                                    <div class="col-6 pr-0  pl-12">
                                                        <div class="firm-name mb-3">
                                                            <label class="Caption-M">
                                                                Firm Name
                                                            </label>
                                                            <div [ngClass]="!editMode ? 'textColor ' : 'textColor '">
                                                                <div class="fontStyle14 mL"
                                                                    *ngIf="!editMode; else firmType">
                                                                    {{selectedDocument?.firmName}}
                                                                </div>
                                                                <ng-template #firmType>                                                                  
                                                                    
                                                                    <kendo-combobox [valuePrimitive]="true" [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                                                        [(ngModel)]="selectedDocument.firmId" #firmName="ngModel" [fillMode]="'flat'" [filterable]="true" name="firmName"
                                                                        [virtual]="virtual" class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                                                        [size]="'medium'" [data]="firms" [filterable]="true" textField="firmName" valueField="firmID"
                                                                        [placeholder]="selectedDocument.firmName !== '-' ? selectedDocument.firmName : 'Search or select firm'"
                                                                        (valueChange)="asDocUpdated(selectedDocument.firmId,'firmName')">
                                                                    </kendo-combobox>
                                                                    <div *ngIf="documentError.ShowOtherError"
                                                                        class="od_Validation1 nep-error positionabsolute">
                                                                    </div>
                                                                </ng-template>
                                                            </div>
                                                        </div>
                                                        <div class="fund-name mb-3">
                                                            <label class="Caption-M">
                                                                Fund Name
                                                            </label>
                                                            <div [ngClass]="!editMode ? 'textColor ' : 'textColor '">
                                                                <div class="fontStyle14 mL"
                                                                    *ngIf="!editMode; else fundName">
                                                                    {{selectedDocument?.fundName}}
                                                                </div>
                                                                <ng-template #fundName>
                                                                    <kendo-combobox [valuePrimitive]="true" [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                                                        [(ngModel)]="selectedDocument.fundId" #fundID="ngModel" [fillMode]="'flat'" [filterable]="true" name="fundID"
                                                                        [virtual]="virtual" class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                                                        [size]="'medium'" [data]="funds" [filterable]="true" textField="fundName" valueField="fundID"
                                                                        [placeholder]="selectedDocument.fundName !== '-' ? selectedDocument.fundName : 'Search or select fund'"
                                                                        (valueChange)="hasDocUpdated(selectedDocument.fundId,'fundName')">
                                                                    </kendo-combobox>
                                                                    <div *ngIf="documentError.ShowOtherError"
                                                                        class="od_Validation1 nep-error positionabsolute">
                                                                    </div>
                                                                </ng-template>
                                                            </div>
                                                        </div>
                                                        <div class="portfolio-company-name mb-3">
                                                            <label class="Caption-M" >
                                                                Portfolio Company Name
                                                            </label>
                                                            <div [ngClass]="!editMode ? 'textColor ' : 'textColor '">
                                                                <div class="fontStyle14 mL"
                                                                    *ngIf="!editMode; else portfolioCompanyName">
                                                                    {{selectedDocument?.portfolioCompanyName}}
                                                                </div>
                                                                <ng-template #portfolioCompanyName>
                                                                    <kendo-combobox [valuePrimitive]="true" [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                                                        [(ngModel)]="selectedDocument.portfolioCompanyId" #portfolioCompany="ngModel" [fillMode]="'flat'" [filterable]="true" name="portfolioCompany"
                                                                        [virtual]="virtual" class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                                                        [size]="'medium'" [data]="porfoliocompanies" [filterable]="true" textField="companyName" valueField="portfolioCompanyID"
                                                                        [placeholder]="selectedDocument.portfolioCompanyName !== '-' ? selectedDocument.portfolioCompanyName : 'Search or select company'"
                                                                        (valueChange)="hasDocUpdated(selectedDocument.portfolioCompanyId,'portfolioCompanyName')">
                                                                    </kendo-combobox>
                                                                    <div *ngIf="documentError.ShowOtherError"
                                                                        class="od_Validation1 nep-error positionabsolute">
                                                                    </div>
                                                                </ng-template>
                                                            </div>
                                                        </div>
                                                        <div class="deal-id mb-3">
                                                            <label class="Caption-M" >
                                                                Deal ID
                                                            </label>
                                                            <div class="textColor  fontStyle14">
                                                                <div class="fontStyle14 mL"
                                                                    *ngIf="!editMode; else dealId">
                                                                    {{selectedDocument?.dealName}}
                                                                </div>
                                                                <ng-template #dealId>
                                                                    <kendo-combobox [valuePrimitive]="true" [clearButton]="false" [kendoDropDownFilter]="filterSettings"
                                                                    [(ngModel)]="selectedDocument.dealId" #dealId="ngModel" [fillMode]="'flat'" [filterable]="true" name="dealId"
                                                                    [virtual]="virtual" class="k-dropdown-width-100 k-select-medium k-select-flat-custom k-dropdown-height-35"
                                                                    [size]="'medium'" [data]="deals" [filterable]="true" textField="dealCustomID" valueField="dealID"
                                                                    [placeholder]="selectedDocument.dealName !== '-' ? selectedDocument.dealName : 'Search deal ID'"
                                                                    (valueChange)="hasDocUpdated(selectedDocument.dealId,'dealName')">
                                                                </kendo-combobox>
                                                                    <div *ngIf="documentError.ShowOtherError"
                                                                        class="od_Validation1 nep-error positionabsolute">
                                                                    </div>
                                                                </ng-template>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                                <div class="row mr-0 ml-0">
                                                    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 assigned-to mb-3">
                                                        <label class="Caption-M">
                                                            Assigned to
                                                        </label>
                                                        <div *ngIf="!editMode && selectedAssignedTo.length === 0"
                                                            class="textColor fontStyle14">
                                                            -
                                                        </div>
                                                        <div class="textColor fontStyle14 chipsDiv"
                                                            *ngIf="!editMode && selectedAssignedTo.length > 0">
                                                            <div class="container" [class.show]="showassign">
                                                                <span *ngIf="selectedAssignedTo.length<3">
                                                                    <chip [isReadonly]=true
                                                                        [chipitems]="selectedAssignedTo"
                                                                       
                                                                        customclass="mat-chip mat-primary mat-standard-chip circularOutlined textColor fontStyle14">
                                                                    </chip>
                                                                </span>
                                                                <span
                                                                    *ngIf="selectedAssignedTo.length>2 && !showassign">
                                                                    <chip [isReadonly]=true
                                                                        [chipitems]="selectedAssignedTo.slice(0, 2)"
                                                                        
                                                                        customclass="mat-chip mat-primary mat-standard-chip circularOutlined textColor fontStyle14">
                                                                    </chip>
                                                                </span>
                                                                <span *ngIf="selectedAssignedTo.length>2 && showassign">
                                                                    <chip [isReadonly]=true
                                                                        [chipitems]="selectedAssignedTo"
                                                                       
                                                                        customclass="mat-chip mat-primary mat-standard-chip circularOutlined textColor fontStyle14">
                                                                    </chip>
                                                                </span>
                                                            </div>
                                                            <span *ngIf="selectedAssignedTo.length>2"
                                                                class="showHandIcon moreless"
                                                                (click)="showassign = !showassign">{{showassign ?
                                                                'Less': '+'+selectedAssignedTo.length-2+'
                                                                More'
                                                                }}</span>
                                                        </div>
                                                        <div *ngIf="editMode" class="textColor fontStyle14 opn-doc-mar-tp">
                                                            <chip maxlen="50" [isReadonly]=false
                                                                (removeItem)="removeAssignedToItem($event)"
                                                                [chipitems]="selectedAssignedTo"
                                                               
                                                                customclass="mat-chip mat-primary mat-standard-chip circularOutlined textColor fontStyle14"
                                                                [allItems]="allUsers"
                                                                (addSelectedItem)="getAssignees($event)"></chip>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                                                    <div class="row mr-0 ml-0">
                                                        <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 tag-control mb-3">
                                                            <label class="Caption-M">
                                                                Tags
                                                            </label>
                                                            <div *ngIf="!editMode && selectedDocument?.tags === ''"
                                                                class="textColor fontStyle14">
                                                                -
                                                            </div>
                                                            <div class="chipsDiv"
                                                                *ngIf="!editMode && selectedDocument?.tags !== ''">
                                                                <div class="container" [class.show]="showatags">
                                                                    <span
                                                                        *ngIf="selectedDocument?.tags?.split(',').length<3">
                                                                        <chip
                                                                            [chipitems]="selectedDocument?.tags?.split(',')"
                                                                            customclass="mat-chip mat-primary mat-standard-chip circularOutlined fontStyle14 mL">
                                                                        </chip>
                                                                    </span>
                                                                    <span
                                                                        *ngIf="selectedDocument?.tags?.split(',').length>2 && !showatags">
                                                                        <chip
                                                                            [chipitems]="selectedDocument?.tags?.split(',').slice(0, 2)"
                                                                            customclass="mat-chip mat-primary mat-standard-chip circularOutlined fontStyle14 mL">
                                                                        </chip>
                                                                    </span>
                                                                    <span
                                                                        *ngIf="selectedDocument?.tags?.split(',').length>2 && showatags">
                                                                        <chip
                                                                            [chipitems]="selectedDocument?.tags?.split(',')"
                                                                            customclass="mat-chip mat-primary mat-standard-chip circularOutlined fontStyle14 mL">
                                                                        </chip>
                                                                    </span>
                                                                </div>
                                                                <span
                                                                    *ngIf="selectedDocument?.tags?.split(',').length>2"
                                                                    class="showHandIcon moreless"
                                                                    (click)="showatags = !showatags">{{ showatags ?
                                                                    'Less':
                                                                    "+"+selectedDocument?.tags?.split(',').length-2+'
                                                                    More' }}</span>
                                                            </div>
                                                            <div *ngIf="editMode" id="tagChips"
                                                               class="opn-doc-mar-tp">
                                                                <chip maxlen="50" [isReadonly]=false
                                                                    (removeItem)="removeTagItem($event)"
                                                                    (addItem)="addTagItem($event)"
                                                                    [chipitems]="documentTags"
                                                                   
                                                                    customclass="mat-chip mat-primary mat-standard-chip circularOutlined textColor fontStyle14">
                                                                </chip>
                                                            </div>

                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                                                    <div class="row mr-0 ml-0">
                                                        <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 mb-3">
                                                            <label class="Caption-M">
                                                                Comments
                                                            </label>
                                                            <div class="textColor fontStyle14">
                                                                <textarea
                                                                    class="openDoctextarea fontStyle14 comment-textarea"
                                                                    placeholder="Enter comments here…" autocomplete="off" rows="2"
                                                                    cols="50" name="comment"
                                                                    (input)="hasDocUpdated($event,'comment')"
                                                                    [(ngModel)]="selectedDocument.comment"
                                                                    maxlength="500"></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0 footerStyle d-block mb-3">
                                <div class="footerCol1">
                                    <span class="lastModifiedStyle">
                                        Last modified on {{selectedDocument?.modifiedOn | date:'dd-MMM-yyyy'}}
                                    </span>
                                </div>
                                <div class="footerCol2">
                                    <button class="nep-button nep-button-secondary cancelCol" (click)="closeDoc()"
                                        *ngIf="!editMode; else cancelButton">Close</button>
                                    <ng-template #cancelButton>
                                        <button class="nep-button mr-0 nep-button-secondary cancelCol"
                                            (click)="showCancelModal()">
                                            Cancel
                                        </button>
                                    </ng-template>
                                    <button id="editDoc" class=" nep-button nep-button-primary"
                                        *ngIf="!editMode; else saveButton" (click)="onEditDocument()">Edit
                                    </button>
                                    <ng-template #saveButton>
                                        <button id="editDoc" class="nep-button nep-button-primary"
                                            (click)="onConfirmSave()" [disabled]="!isDocUpdated">Save
                                        </button>
                                    </ng-template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="add-user-component right-pannel-style">
                    <div class="card card-main">

                        <div class="card-body ">
                            <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                                <div class="row mr-0 ml-0">
                                    <div class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                                        <div class="row mr-0 ml-0 repository-right-pannel-height">
                                            <div id="rightContainerHeader" class="w100 rBorder right-header-style">
                                                    <div class="pl-12 float-left search-pannel-style search-parent-div">
                                                        <input type="text"
                                                        class="searchBox fontStyle14 search-style-btn"
                                                        placeholder="Search within file contents" />
                                                    <img class="searchIcon" src="assets/dist/images/Search.svg"
                                                        alt="" />
                                                      
                                                    </div>
                                                    <div class="w100 float-right right-pannel-icon-style">

                                                        <div class="download-icon-top-padding">
                                                            <img class="showHandIcon opn-doc-mar-tp"
                                                                src="assets/dist/images/Cloud-download.svg" alt=""
                                                                (click)="DownloadFile()" id="download-file-edit-repo" />
                                                                <i aria-hidden="true" *ngIf="isDownload" class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                                        </div>
                                                        <div class="d-inline cloud-icon pl-2 divider-custom-style">
                                                        </div>

                                                        <div>
                                                            <img class="rHeaderDelIcons showHandIcon" id="showCancelDeleteModal"
                                                                src="assets/dist/images/delete.svg" alt="" 
                                                                (click)="showCancelDeleteModal()" />
                                                        </div>
                                                    </div>
                                            </div>
                                            <div id="rightContainerBody" class="w100 doc-w100-flxwrp">
                                                <div *ngIf="loading; then Loader; else Viewer"></div>
                                                <ng-template #Loader>
                                                    <div class="pdf-loading-container">
                                                        <img class="loaderpreview"
                                                            src="assets/dist/images/loading-small.gif" alt="" />
                                                        <div class="loading-text">
                                                            <p>Loading PDF...</p>
                                                            <p *ngIf="isPdfReloading" class="reload-text">
                                                                Reloading PDF (Attempt {{pdfLoadAttempt}}/{{maxPdfLoadAttempts}})
                                                            </p>
                                                        </div>
                                                    </div>
                                                </ng-template>
                                                <ng-template #Viewer>
                                                    <span *ngIf="selectedDocument?.extension=='.pdf' && !isPreviewMode">
                                                        <nep-pdfviewer [pdfsource]="pdfblobvalue.body"></nep-pdfviewer>
                                                    </span>
                                                    <div *ngIf="selectedDocument?.extension!='.pdf' || isPreviewMode"
                                                        class="fontStyle14 preview row mr-0 ml-0 d-flex justify-content-center align-items-center">
                                                        <div
                                                            class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                                                            <div class="row mr-0 ml-0">
                                                                <div
                                                                    class="col-12 col-md-12 col-sm-12 col-lg-12 col-xl-12 pr-0 pl-0">
                                                                    <div
                                                                        class="download-icon-top-padding d-flex flex-column align-items-center">
                                                                        <img class="showHandIcon opn-doc-mar-tp"
                                                                            src="assets/dist/images/Empty-state (illustrations)-repository.svg" />
                                                                        <span class="no-data-preview">Uh-oh! No preview is available for this
                                                                            file</span>
                                                                        <span>
                                                                            <button id="editDoc"
                                                                            class="nep-button nep-button-primary showHandIcon"
                                                                            (click)="DownloadFile(true)" id="download-btn"
                                                                            *ngIf="selectedDocument?.extension != '.pdf'">Download
                                                                            File
                                                                        </button>
                                                                        <i aria-hidden="true" *ngIf="isPreviewDownload"
                                                                            class="fa fa-spinner fa-pulse fa-1x fa-fw"></i>
                                                                        </span>
                                                                        
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </ng-template>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div *ngIf="cancelEditing">
        <confirm-modal [primaryButtonName]="primaryButtonName" [secondaryButtonName]="secondaryButtonName"
            [modalTitle]="modalTitle" (primaryButtonEvent)="onCancelEditing()"
            (secondaryButtonEvent)="closeCancelModal()">
            <div class="modalBodyTextStyle">
                <div>
                    {{modalBody1}}
                </div>
                <div>
                    {{modalBody2}}
                </div>
            </div>
        </confirm-modal>
    </div>

    <div *ngIf="cancelDeleteDoc">
        <confirm-modal [primaryButtonName]="primaryButtonName" [secondaryButtonName]="secondaryButtonName"
            [modalTitle]="deleteModalTitle" (primaryButtonEvent)="onYesDeleteModal()"
            (secondaryButtonEvent)="onNoDeleteModal()">
            <div class="modalBodyTextStyle">
                <div>
                    {{deleteModalBody1}}
                </div>
                <div>
                    {{deleteModalBody2}}
                </div>
            </div>
        </confirm-modal>
    </div>

    <div *ngIf="confirmSave">
        <confirm-modal [primaryButtonName]="savePrimaryButtonName" [secondaryButtonName]="saveSecondaryButtonName"
            [modalTitle]="saveModalTitle" (primaryButtonEvent)="onUpdateDocument()"
            (secondaryButtonEvent)="onCancelSave()">
            <div class="modalBodyTextStyle">
                <div>
                    {{saveModalBody1}}
                </div>
                <div>
                    {{saveModalBody2}}
                </div>
            </div>
        </confirm-modal>
    </div>
    <div *ngIf="confirmChangeFolder">
        <confirm-modal modalTitle="Change Folder" primaryButtonName="Yes" secondaryButtonName="No"
            (primaryButtonEvent)="onChangeFolderApproved($event)"
            (secondaryButtonEvent)="OnChangeFolderCancelled($event)">
            <div class="modalBodyTextStyle">
                <div *ngIf="!ShowValidation || selectedDocument.folderName === 'Uploaded'">
                    You are changing the document location from {{PreviousFolder}} to {{selectedDocument.folderName}}
                    Files.
                    Are you sure you want to continue?
                </div>
                <div *ngIf="ShowValidation && selectedDocument.folderName === 'Final'">
                    To continue, please enter Document Name, Type, Sub Type, Document Date and any one from Firm Name,
                    Fund
                    Name, Portfolio Company Name OR Deal ID.
                    <p>Are you sure you want to continue?</p>
                </div>
            </div>
        </confirm-modal>
    </div>
</div>

<app-loader-component *ngIf="loading"></app-loader-component>