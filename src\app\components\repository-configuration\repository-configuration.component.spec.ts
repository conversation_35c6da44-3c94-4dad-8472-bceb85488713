import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RepositoryConfigurationComponent } from './repository-configuration.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { ActivatedRoute, Router } from '@angular/router';
import { of } from 'rxjs';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { PageConfigurationService } from 'src/app/services/page-configuration.service';

describe('RepositoryConfigurationComponent', () => {
  let component: RepositoryConfigurationComponent;
  let fixture: ComponentFixture<RepositoryConfigurationComponent>;
  let pageConfigServiceSpy: jasmine.SpyObj<PageConfigurationService>;

  beforeEach(async () => {
    pageConfigServiceSpy = jasmine.createSpyObj('PageConfigurationService', ['getPageConfigSettingById']);
    pageConfigServiceSpy.getPageConfigSettingById.and.returnValue(of({ subPageList: [] }));

    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      declarations: [RepositoryConfigurationComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { params: {} },
            params: of({}),
            queryParams: of({})
          }
        },
        { provide: 'BASE_URL', useValue: '' },
        { provide: PageConfigurationService, useValue: pageConfigServiceSpy }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(RepositoryConfigurationComponent);
    component = fixture.componentInstance;
    // Set up tabs and activeTab to match component logic
    component.tabs = [
      { name: 'Data Collection', aliasname: 'Data Collection', active: true, hidden: false },
      { name: 'Email Notification', aliasname: 'Email Notification', active: false, hidden: false }
    ];
    component.activeTab = 'Data Collection';
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  describe('onTabClick', () => {
    it('should set activeTab to the clicked tab name', () => {
  const clickedTab: ITab = { name: 'Email Notification', aliasname: 'Email Notification', active: false, hidden: false };
      component.onTabClick(clickedTab);
      expect(component.activeTab).toBe('Email Notification');
    });

    it('should set active property to true for clicked tab', () => {
      // Ensure both name and aliasname match the component logic
      const clickedTab: ITab = { name: 'Email Notification', aliasname: 'Email Notification', active: false, hidden: false };
      component.tabs = [
        { name: 'Data Collection', aliasname: 'Data Collection', active: true, hidden: false },
        { name: 'Email Notification', aliasname: 'Email Notification', active: false, hidden: false }
      ];
      component.activeTab = 'Data Collection';
      component.onTabClick(clickedTab);
      const updatedTab = component.tabs.find(t => t.aliasname === 'Email Notification');
      expect(updatedTab?.active ?? false).toBe(true);
    });

    it('should set active property to false for all other tabs', () => {
      const clickedTab: ITab = { name: 'Email Notification', aliasname: 'Email Notification', active: false, hidden: false };
      component.tabs = [
        { name: 'Data Collection', aliasname: 'Data Collection', active: true, hidden: false },
        { name: 'Email Notification', aliasname: 'Email Notification', active: false, hidden: false }
      ];
      component.activeTab = 'Data Collection';
      component.onTabClick(clickedTab);
      const unselectedTab = component.tabs.find(t => t.aliasname === 'Data Collection');
      expect(unselectedTab?.active ?? false).toBe(false);
    });

    it('should handle click on already active tab', () => {
      const clickedTab: ITab = { name: 'Data Collection', aliasname: 'Data Collection', active: true, hidden: false };
      component.tabs = [
        { name: 'Data Collection', aliasname: 'Data Collection', active: true, hidden: false },
        { name: 'Email Notification', aliasname: 'Email Notification', active: false, hidden: false }
      ];
      component.activeTab = 'Data Collection';
      component.onTabClick(clickedTab);
      expect(component.activeTab).toBe('Data Collection');
      expect(component.tabs[0]?.active ?? false).toBe(true);
      expect(component.tabs[1]?.active ?? false).toBe(false);
    });

    it('should handle click when tabs array is empty', () => {
  component.tabs = [];
  component.activeTab = undefined as any;
  const clickedTab: ITab = { name: 'Email Notification', aliasname: 'Email Notification', active: false, hidden: false };
  component.onTabClick(clickedTab);
  expect(component.activeTab).toBe('Email Notification');
    });

    it('should maintain hidden property of tabs', () => {
      component.tabs = [
        { name: 'Data Collection', aliasname: 'Data Collection', active: true, hidden: false },
        { name: 'Email Notification', aliasname: 'Email Notification', active: false, hidden: true }
      ];
      component.activeTab = 'Data Collection';
      const clickedTab: ITab = { name: 'Data Collection', aliasname: 'Data Collection', active: true, hidden: false };
      component.onTabClick(clickedTab);
      expect(component.tabs[1].hidden).toBe(true);
    });

    it('should update url to #/repository-configuration if activeTab is in url and active tab is Data Collection', () => {
      component.params = { activeTab: 'Data Collection' };
      spyOn(window.history, 'replaceState');
      component.tabs = [
        { name: 'Data Collection', aliasname: 'Data Collection', active: true, hidden: false },
        { name: 'Email Notification', aliasname: 'Email Notification', active: false, hidden: false }
      ];
      component.activeTab = 'Data Collection';
      const tab: ITab = { name: 'Data Collection', aliasname: 'Data Collection', active: true, hidden: false };
      component.onTabClick(tab);
      expect(window.history.replaceState).toHaveBeenCalledWith({}, '', '#/repository-configuration');
    });
  });

  describe('onFundSelected', () => {
    it('should set selectedFunds to the provided funds array', () => {
      const funds = [{ id: 1, name: 'Fund 1' }, { id: 2, name: 'Fund 2' }];
      component.onFundSelected(funds);
      expect(component.selectedFunds).toEqual(funds);
    });
  });

  describe('onIsFundChange', () => {
    it('should set isFund to the provided boolean value', () => {
      component.isFund = false;
      component.onIsFundChange(true);
      expect(component.isFund).toBe(true);
      component.onIsFundChange(false);
      expect(component.isFund).toBe(false);
    });
  });
});

describe('RepositoryConfigurationComponent Additional Tests', () => {
  let component: RepositoryConfigurationComponent;
  let fixture: ComponentFixture<RepositoryConfigurationComponent>;
  let routerSpy: jasmine.SpyObj<Router>;
  let pageConfigServiceSpy: jasmine.SpyObj<any>;

  beforeEach(async () => {
    routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    pageConfigServiceSpy = jasmine.createSpyObj('PageConfigurationService', ['getPageConfigSettingById']);
    pageConfigServiceSpy.getPageConfigSettingById.and.returnValue(of({ subPageList: [] }));
    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      declarations: [RepositoryConfigurationComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: PageConfigurationService, useValue: pageConfigServiceSpy },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { queryParams: {} },
            queryParams: of({}),
          }
        },
        { provide: 'BASE_URL', useValue: '' }
      ]
    }).compileComponents();
    fixture = TestBed.createComponent(RepositoryConfigurationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should set selectedCompanies when onCompanySelected is called', () => {
    const companies = [{ id: 1, name: 'Company 1' }];
    component.onCompanySelected(companies);
    expect(component.selectedCompanies).toEqual(companies);
  });

  it('should handle onCompanySelected with empty array', () => {
    component.onCompanySelected([]);
    expect(component.selectedCompanies).toEqual([]);
  });

  it('should handle onCompanySelected with undefined', () => {
    component.onCompanySelected(undefined as any);
    expect(component.selectedCompanies).toBeUndefined();
  });

  it('should navigate to /email-configuration when navigateToEmailConfig is called', () => {
    component.navigateToEmailConfig();
    expect(routerSpy.navigate).toHaveBeenCalledWith(['/email-configuration']);
  });

  it('should initialize activeTab and tabs correctly', () => {
    component.tabs = [
      { name: 'Data Collection', aliasname: 'Data Collection', active: true, hidden: false },
      { name: 'Email Notification', aliasname: 'Email Notification', active: false, hidden: false }
    ];
    component.activeTab = 'Data Collection';
    expect(component.activeTab).toBe('Data Collection');
    expect(component.tabs.length).toBeGreaterThan(0);
    expect(component.tabs[0].active).toBeTrue();
  });

  it('should handle onTabClick with a tab not present in tabs', () => {
    component.tabs = [
      { name: 'Data Collection', aliasname: 'Data Collection', active: true, hidden: false },
      { name: 'Email Notification', aliasname: 'Email Notification', active: false, hidden: false }
    ];
    component.activeTab = 'Data Collection';
    const fakeTab: ITab = { name: 'Nonexistent Tab', aliasname: 'Nonexistent Tab', active: false, hidden: false };
    component.onTabClick(fakeTab);
    expect(component.activeTab).toBe('Nonexistent Tab');
    // No tab in tabs should be active
    expect(component.tabs.every(t => !t.active)).toBeTrue();
  });

  it('should not throw if onTabClick is called with tab not in tabs', () => {
    component.tabs = [];
    expect(() => component.onTabClick({ name: 'X', aliasname: 'X', active: false, hidden: false })).not.toThrow();
  });

  it('should handle onFundSelected with undefined', () => {
    component.onFundSelected(undefined as any);
    expect(component.selectedFunds).toBeUndefined();
  });

  it('should handle onIsFundChange with undefined', () => {
    component.onIsFundChange(undefined as any);
    expect(component.isFund).toBeUndefined();
  });

  it('should handle missing queryParams in ngOnInit', () => {
    const route = TestBed.inject(ActivatedRoute);
    (route.queryParams as any) = of({});
    component.tabs = [
      { name: 'Data Collection', aliasname: 'Data Collection', active: true, hidden: false },
      { name: 'Email Notification', aliasname: 'Email Notification', active: false, hidden: false }
    ];
    component.activeTab = 'Data Collection';
    component.ngOnInit();
    expect(component.activeTab).toBe('Data Collection');
  });

  // --- Additional tests for getPageConfigSetting edge cases ---
  it('should handle getPageConfigSetting with null result', () => {
    pageConfigServiceSpy.getPageConfigSettingById.and.returnValue(of(null));
    component.getPageConfigSetting();
    expect(component.tabs.length).toBe(0);
  });

  it('should handle getPageConfigSetting with empty subPageList', () => {
    pageConfigServiceSpy.getPageConfigSettingById.and.returnValue(of({ subPageList: [] }));
    component.getPageConfigSetting();
    expect(component.tabs.length).toBe(0);
  });

  it('should handle getPageConfigSetting with all inactive tabs', () => {
    pageConfigServiceSpy.getPageConfigSettingById.and.returnValue(of({ subPageList: [{ isActive: false }] }));
    component.getPageConfigSetting();
    expect(component.tabs.length).toBe(0);
  });

  it('should set first tab as active if available', () => {
    const subPageList = [
      { displayName: 'Tab1', name: 'Tab1', isActive: true },
      { displayName: 'Tab2', name: 'Tab2', isActive: true }
    ];
    pageConfigServiceSpy.getPageConfigSettingById.and.returnValue(of({ subPageList }));
    component.getPageConfigSetting();
    expect(component.activeTab).toBe('Tab1');
    expect(component.tabs[0].active).toBeTrue();
  });

  it('should set params from route.snapshot.queryParams in constructor', () => {
    expect(component.params).toEqual({});
  });
});