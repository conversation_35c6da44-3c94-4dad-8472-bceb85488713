<?xml version="1.0" encoding="UTF-8"?>
<configuration>
   <system.web>
        <customErrors mode="Off"/>
    </system.web>
   <system.webServer>
      <httpProtocol>
         <customHeaders>
            <clear />
            <remove name="X-Frame-Options" />
            <remove name="X-Content-Type-Options" />
            <remove name="X-Powered-By" />
            <add name="X-Frame-Options" value="SAMEORIGIN" />
            <add name="Server" value="Webserver" />
            <add name="X-Content-Type-Options" value="nosniff" />
            <add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
            <add name="Cache-Control" value="no-cache, no-store, must-revalidate, pre-check=0, post-check=0, max-age=0, s-maxage=0" />
            <add name="Pragma" value="no-cache" />
            <add name="Expires" value="0" />
            <add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains" />
            <add name="AKP-BEAT-Foliosure-Header" value="AKP-BEAT-Foliosure" />
            <add name="Content-Security-Policy" value="default-src 'self'; 
script-src 'self' https://dl.revealbi.io 'unsafe-inline' 'unsafe-eval' app.eu.pendo.io pendo-eu-static.storage.googleapis.com cdn.eu.pendo.io  pendo-eu-static-5344761307791360.storage.googleapis.com data.eu.pendo.io;  
 style-src 'self' 'unsafe-inline' 'unsafe-eval' https://asmt.identity.beat-services.net https://monmouth.identity.beat-services.net https://bristol.identity.beat-services.net https://admont.identity.beat-services.net https://demo.identity.beat-services.net https://identity.beat-services.net https://uat.beatapps.net https://dl.revealbi.io https://fast.fonts.net 'unsafe-inline' app.eu.pendo.io cdn.eu.pendo.io pendo-eu-static-5344761307791360.storage.googleapis.com;  
 img-src 'self' data: http://localhost:3000 https://asmt.identity.beat-services.net https://monmouth.identity.beat-services.net https://bristol.identity.beat-services.net https://admont.identity.beat-services.net https://demo.identity.beat-services.net https://identity.beat-services.net https://uat.beatapps.net https://dl.revealbi.io cdn.eu.pendo.io app.eu.pendo.io pendo-eu-static-5344761307791360.storage.googleapis.com  data.eu.pendo.io ;  
 connect-src 'self' blob: data: http://localhost:3000 https://asmt.identity.beat-services.net https://monmouth.identity.beat-services.net https://bristol.identity.beat-services.net https://admont.identity.beat-services.net https://demo.identity.beat-services.net https://identity.beat-services.net https://uat.beatapps.net https://dev.beatapps.net https://test.beatapps.net https://demo.beatfoliosure.com https://trial.beatfoliosure.com https://taabo-ch.beatfoliosure.com https://exeter.beatfoliosure.com 
 https://asmt.beatfoliosure.com https://monmouth.beatfoliosure.com https://bristol.beatfoliosure.com https://perf-test01.beatapps.net wss://uat.beatapps.net wss://dev.beatapps.net wss://perf-test01.beatapps.net wss://test.beatapps.net wss://demo.beatfoliosure.com wss://trial.beatfoliosure.com wss://taabo-ch.beatfoliosure.com wss://exeter.beatfoliosure.com wss://asmt.beatfoliosure.com  wss://monmouth.beatfoliosure.com wss://bristol.beatfoliosure.com wss://localhost:5001 https://localhost:5001 https://localhost:7285 https://localhost:7288 http://localhost:7288 https://fonts.googleapis.com wss://dev.beatapps.net/ app.eu.pendo.io data.eu.pendo.io pendo-eu-static-5344761307791360.storage.googleapis.com;  frame-src 'self' http://localhost:3000 http://dev.beatapps.net/ https://dev.beatapps.net/ http://test.beatapps.net/ https://test.beatapps.net/ http://uat.beatapps.net/ https://uat.beatapps.net/ https://asmt.identity.beat-services.net http://asmt.identity.beat-services.net http://monmouth.identity.beat-services.net https://monmouth.identity.beat-services.net http://bristol.identity.beat-services.net https://bristol.identity.beat-services.net http://admont.identity.beat-services.net https://admont.identity.beat-services.net http://demo.identity.beat-services.net https://demo.identity.beat-services.net http://identity.beat-services.net https://identity.beat-services.net http://uat.beatapps.net https://uat.beatapps.net http://app.eu.pendo.io https://app.eu.pendo.io; 
 frame-ancestors 'self' http://test.beatapps.net https://test.beatapps.net http://test.beatapps.net/foliosure/test/pod/app/ https://test.beatapps.net/foliosure/test/pod/app/ http://test.beatapps.net/foliosure/test/pod/ingestion-ui https://test.beatapps.net/foliosure/test/pod/ingestion-ui http://dev.beatapps.net/foliosure/feature/pod/app/ https://dev.beatapps.net/foliosure/feature/pod/app/ http://dev.beatapps.net/foliosure/feature/pod/ingestion-ui/ https://dev.beatapps.net/foliosure/feature/pod/ingestion-ui/ http://uat.beatapps.net/foliosure/feature/pod-a/app/ https://uat.beatapps.net/foliosure/feature/pod-a/app/ http://uat.beatapps.net/foliosure/feature/pod/ingestion-ui https://uat.beatapps.net/foliosure/feature/pod/ingestion-ui http://dev.beatapps.net/foliosure/feature/pod-a/app/ https://dev.beatapps.net/foliosure/feature/pod-a/app/ http://dev.beatapps.net/foliosure/feature/pod/ingestion-ui https://dev.beatapps.net/foliosure/feature/pod/ingestion-ui ;
 child-src 'self' app.eu.pendo.io; form-action 'self'; 
 object-src 'none';
 font-src 'self' https://fonts.gstatic.com;" />

         </customHeaders>
      </httpProtocol>
      <directoryBrowse enabled="false" />
      <security>
         <requestFiltering removeServerHeader="true">
            <verbs allowUnlisted="false">
               <clear />
               <add verb="GET" allowed="true" />
            </verbs>
         </requestFiltering>
      </security>
      <rewrite>
            <rules>
                <rule name="Foliosure_APP" stopProcessing="true">
                  <match url=".*"/>
                  <conditions logicalGrouping="MatchAll">
                     <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                     <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                  </conditions>
                  <action type="Rewrite" url="./"/>
               </rule>
            </rules>
        </rewrite>
   </system.webServer>
</configuration>